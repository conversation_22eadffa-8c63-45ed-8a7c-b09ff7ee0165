　　第4章：捕捉彗星
   
   数据显示，哥伦布星系有不少的流浪彗星。在250个地球天之后，会有一颗巨大的彗星经过冀星附近，在离冀星500万公里处掠过。

    这颗彗星体积足足有月球的体积三分之一大小。对于冀星来说，这样体积的水应该是够了。

     这是一次难得的机会，如果错过了，等下一颗彗星经过附近，要等120多年了。

　　但怎么捕捉这颗彗星是个问题！

     SOSO给出的方案是派出战斗机群用量子光刀先把彗星切割成可拖动的一块块碎片，再用纳米网罩住，用巡航舰牵引过来，然后再投到冀星去。这样做相对保险，而且操作容易，成功率达百分之九十五以上。

     但我并不是这样想，SOSO的方案是有一定的可行性，可是耗时太长，而且可捕捉的水资源有限。

　　彗星的速度虽然比不上战斗飞船，但也算是很快的了，母舰里也只有500艘巡航舰。等到这500艘巡航舰各自捕捉一块彗星碎片回到冀星，彗星主体都不知道飞了哪里去了。

     那么大体积的彗星，只能拿到那么一点水资源，我觉得不划算。冀星如果等下一颗彗星出现，是120年后了。

　　时间太长了。我等不了

　　如果每次彗星经过都只拿到一点点的水资源，那冀星何年何月才能有足够的水？虽然说可以用同样的方法去星系各处去捕捉彗星碎片，但太折腾，太耗能源了。我否定了SOSO的方案。

     需要捕捉整一颗彗星。一次性的捕捉整一颗彗星，这样就不用来回折腾，但这样的难度可想而知。

     我最终确定的方案是这样的。先派出机器人登陆彗星，埋好量子炸弹，先将彗星解题成几大块，再将大块分别炸成小块。

　　然后用巡航舰群在彗星前进的路径附近，从一个角度同时发射量子炮弹去轰炸，利用量子炮弹爆炸的产生的波动来改变小块彗星的行进轨迹，像驱赶羊群一样将小块彗星推至冀星的引力轨道上。让这些碎片被冀星的引力所捕捉，围绕着冀星公转。然后再用量子光刀切块，一块一块的往冀星上送，在冀星上空再将每一块彗星碎片击碎，让它们自行掉落就好了。

     SOSO却不同意这样的计划。它计算过，我的计划耗能比它的计划要高出1000%。需要同时操控500艘巡航舰来发射，而且量子炮弹每次爆炸要非常准确。

　　还要计算出彗星在冀星上的轨道位置，如果彗星碎片到达冀星指定的轨道位置时候速度过快，有可能会被抛出去，如果速度太慢，彗星会直接撞向冀星。所以还要计算好角度用量子炮弹给彗星减速。这种操作难度系数太大，而且结果不可预测，成功率只有百分之四十五。

    SOSO建议我重新考虑方案。但我坚持要它执行。这样的机会不多，无论如何也要试一下，我觉得假如人类科学家们在身边，他们也会同意我这个方案的。

　就这样，我操控这母舰迎着彗星要过来的方向驶去。

    我们准备从距离彗星300万公里处开始操作机器人进行登陆，母舰的500艘巡航舰也全部飞了出去，由SOSO操控着。

    我则是在母舰上根据飞船回传的数据及时调整和指挥SOSO的操控参数。

    我们配合的很好，很顺利地派遣机器人登陆彗星，选好炸弹埋点，飞船逃离，择时引爆，一气呵成。

　　然后再登陆分解出来的大块彗星上，继续选好炸弹埋点，飞船逃离，择时引爆。

　　彗星按照原定的计划，被炸成了无数碎片，还沿着原来的轨道向着冀星飞来。

　　500艘巡航舰也回到母舰附近，准备执行下一阶段的计划。

    开始一切似乎很顺利，彗星被分解后，按照我们规划好的路线飞行着。但行程到一半才发现不对，本来设定好的，由300艘飞船发射炮弹来改变彗星碎片速度，另外剩200艘飞船在彗星前进的位置用来适当减速彗星碎片。

   但由于量子炮弹威力过大，而且发射过于频繁，彗星碎片改变的角度和速度都超出了原来的预算值。使得负责减速的飞船应付不过来，眼看着彗星的速度比原来的速度越来越快，而且根据行进的路径判断，这些碎片会径直撞向冀星。

    那么多彗星碎片撞上冀星的话，估计整个行星都毁了。就算不毁，撞击星球击起的尘埃，还有引发的大规模火山喷发出来的火山灰，估计要遮住冀星几万年，整个冀星会进入漫长的冰冻期，那我创造生命的伟大计划就要搁置几万年了。

　　这个例子上古时期的地球上早已经有过。如果冀星真毁了，或者进入了冰冻期，那我不是又得重新找出类似的其他行星？这个星系上合适的行星还真是少之又少，我可不想等几万年。所以，我是不允许这样的事情发生。

    我命令所有的巡航舰全速超过彗星，快速集结到冀星上空，等彗星进入冀星的引力范围，马上发射量子炮弹，直接击碎彗星。将彗星碎片分解成碎片粉末，减少对冀星的撞击力度。
　　
    一时间，冀星的上空，一阵阵闪烁，像人类放的烟花。500艘巡航舰同时不断发射的量子炮弹像雨点一样密集地打到彗星上。母舰也打出量子光刀来帮忙。

　　几分钟的时间，那么大体积的彗星变成了无数块小碎片纷纷跌落在冀星上。撞击的冀星一阵阵的震动。从母舰上观察，可以清楚看到彗星碎片掉落在冀星上所击起的尘埃，几乎覆盖了半个冀星。

　　还有好几处的山脉发生了强烈的地震，引发全球的小规模火山爆发。在巡航舰上都能感觉出冀星的地动山摇，就好像是整个星球在扭动身体迎接彗星的到来。

    彗星碎片落在滚烫的地面上，激起一片片的尘埃。有部分彗星在坠落的过程中直接气化了，变成半空中的白雾，半个星球像被仙雾笼罩着，甚是壮观！

    看着最后一块碎片跌落地面，我松了一口气。虽然好险，但总算成功了。冀星逃过了被毁灭的一劫。我兴奋地挥挥手，对SOSO大喊：give me five!  SOSO用完全沉默来回应我。

    冀星的大地震和各处火山爆发持续了差不多2年的时间，才开始慢慢安静下来。接着下了一场大暴雨，在冀星赤道到南北纬30度之间。这应该是冀星形成以来的第一场雨。这场大暴雨一下就整整下了160年的时间。

    终于，在160年暴雨的冲刷下，我辛苦捕捉来的彗星化作了冀星的河流，湖泊，海洋，还有山顶的积雪！碳基生命终于有了生存的水资源基础。

    冀星原来低洼的地带、平原、戈壁、山脉之间的沟壑全部变成了河流、海洋，海洋将冀星分化成了3个大陆！3个大陆完全被水分隔，还有大大小小无数的小岛屿。

　　这3个大陆，我分别分别命名为1号大陆，2号大陆，3号大陆，名字简洁好记。虽然SOSO表示过抗议，但不重要。

    监测数据显示，冀星有些许氧气，但含量很低。它终于开始有点地球的样子了。除了没有任何生命。

    在冀星下大暴雨的这160年里，我呆在巡航舰里也没有闲着。我把SOSO改造成了智能机器人，当然它之前就是智能AI，我说的是我把它改成了类人机器人，和我一样。

    我开玩笑地想把SOSO改成机器狗的，但遭到SOSO的强烈反对。还以自毁作为要挟，要求我把它改造成2.8米高，有力量感的帅气机器人。并作出承诺：保证以后会严格执行我的命令等等。

　　经过多轮的讨价还价，我最终将它改造成了一名人类女性的形象。嘿。它身高175cm，异常漂亮，身材曼妙，长发飘飘，比我遇到过最漂亮的人类女性还漂亮。

　　SOSO这次没有明确表示反对。

    它的大脑中心处理器部分的基础代码当然是复制了它在母舰上的代码。除此之外，我加入了几千万行代码，让它具有更高的智能，增强了它的学习能力，自我演化能力，当然还增加一些女性的情感特质。

　　当然我无法做到让它像我这样有智慧有情感有理想有风度的四有好青年（暗笑），毕竟怎么制造我是人类的秘密，人类并没有把这部分技术存储在我的大脑里。所以我不能把它变得和我一样。

　　它运转能源我是从巡航舰上抽取了一小部分，安装在它心脏位置，能源需要定期补充。

　　SOSO似乎对于自身很满意，经常披着一个披风在母舰里上下游走。

　　“你知道吗？SOSO。我是根据人类上古神话里面的一个形象改造你的身躯的，你猜是哪个？” 我看着她说

　　“女娲”

　　“聪明”

　　
		“所以。。。你的名字就叫女娲吧”

　　SOSO对这个名字表示满意。还伸出一只手来说：“yeah! Give me five!”

    旧一代的SOSO还是留在母舰里，我还需要它处理分布在哥伦布星系各处的探测器回传的数据，保持对星系的监控，还有以后保持和生命基地的通信。

  

    我想以后建立生命基地，培养生命等等都是需要人手的，我肯定是忙不过来，我也需要副手。而且我最需要的是让SOSO去探测冀星上的各自地表状况，和以后监控生命基地。帮我分担工作。

　　捕捉彗星已经用去了很多的能量资源，而且建立基地也需要大量的能源，我需要它去获取能源。当然了，还有一个重要的原因，我孤独，需要陪伴。

  除此之外，我还和女娲用母舰上的材料制作出一批功能机器人来，这批机器人主要是解决人手问题。

　　我着手设计了生命基地的蓝图。

　　因为我接下来要做的，就是在冀星上建立庞大的生命基地了。

　　我打算把母舰停留在轨道上，我和女娲用200艘巡航舰把机器人运到冀星去，200艘飞船也可以暂时作为营地。留300艘飞船在巡航舰上做备用。

    我选择将所有飞船降落在1号大陆上，在冀星北纬28度左右的高山脚下，这里附近大海。有山有水嘛。这里日照时间长，温度适宜，有充足的水资源。最适合培植生命了。而在山脉附近，方便获取建造材料和基地运转能源。所以，这里算是比较合适的地方了。

    我将飞船缓缓降落在山脚下，这应该是这个星球形成至今，第一次有智能生命踏足吧。从飞船窗边看去，外面是一片白茫茫的大海，安静的像一面镜子，没有一丝波澜，这种安静让我都感觉有点异样。在海的上空飘着了浓密的白云。蓝太阳的光从云层里穿进来，这场景很熟悉，让人感动，母星地球就经常有这样的景象。

    我一想着一个全新的星球即将出现，就感慨万千，真想吟一首诗来抒发一下情感。还没等我开口，女娲就说：“请分配任务，我们开始干活了。”

    我不满的看了它一眼，它摊了摊手说：“反正我不用休息”。

    我调出全息控制系统，输入命令。巡航舰的门徐徐打开，一排排被我制造的机器人就有序的走下飞船。

    大工程正式启动。

　要开始建造生命基地了。
    